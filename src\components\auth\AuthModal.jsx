'use client';
import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import LoginForm from './LoginForm';
import SignupForm from './SignupForm';

const AuthModal = ({ 
  isOpen, 
  onClose, 
  initialMode = 'login' // 'login' or 'signup'
}) => {
  const [mode, setMode] = useState(initialMode);

  const handleSwitchMode = (newMode) => {
    setMode(newMode);
  };

  const handleClose = () => {
    onClose();
    // Reset to initial mode when closing
    setTimeout(() => setMode(initialMode), 300);
  };

  const handleAuthSuccess = () => {
    handleClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="md"
      showCloseButton={true}
      closeOnBackdropClick={true}
      className="max-h-[90vh] overflow-y-auto scrollbar-hide"
    >
      <div className="transition-all duration-300 ease-in-out">
        {mode === 'login' ? (
          <LoginForm 
            onSwitchToSignup={() => handleSwitchMode('signup')}
            onAuthSuccess={handleAuthSuccess}
            isModal={true}
          />
        ) : (
          <SignupForm 
            onSwitchToLogin={() => handleSwitchMode('login')}
            onAuthSuccess={handleAuthSuccess}
            isModal={true}
          />
        )}
      </div>
    </Modal>
  );
};

export default AuthModal;
