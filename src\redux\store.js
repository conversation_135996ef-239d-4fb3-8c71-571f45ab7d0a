import { configureStore } from '@reduxjs/toolkit';
import userReducer from './slices/userSlice';
import themeReducer from './slices/themeSlice';
import cityReducer from './slices/citySlice';
import flightDetailReducer from './slices/flightDetailSlice';
import bookingReducer from './slices/bookingSlice';
import flightReducer from './slices/flightSlice';

import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import storage from 'redux-persist/lib/storage';

const persistConfig = {
  key: 'root',
  storage,
};

const persistedUser = persistReducer(persistConfig, userReducer);
const persistedTheme = persistReducer(persistConfig, themeReducer);

export const store = configureStore({
  reducer: {
    user: persistedUser,
    theme: persistedTheme,
    cities: cityReducer,
    flightDetail: flightDetailReducer,
    flight: flightReducer,
    booking: bookingReducer,

  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export const persistor = persistStore(store);
