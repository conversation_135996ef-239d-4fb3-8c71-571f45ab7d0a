"use client";
import React from 'react';
import FlightCard from './FlightCard';
import AboutSection from './AboutSection';
import InflightFeatures from './InflightFeatures';
import BaggageSection from './BaggageSection';
import FareRules from './FareRules';
import FAQ from './FAQ';
import Sidebar from './DetailSidebar';
import ReviewForm from './ReviewForm';
import Reviews from './Reviews';
// Import icons
import {
  Wifi, Tv, Music, Coffee, AirVent, Utensils, Gamepad2, Wine, Armchair, Image,
  Check, FileText, RotateCcw, DollarSign, AlertTriangle, X,
  Shield, Headphones, MapPin, Plane, Car
} from 'lucide-react';

const FlightDetailPage = ({
  flightData = {},
  aboutData = {},
  inflightData = {},
  fareRulesData = {},
  sidebarData = {},
  apiFlightData = null, // New prop for API data
  className = ""
}) => {
  // Function to map API data to component format
  const mapApiDataToComponentFormat = (apiData) => {
    if (!apiData || !apiData.segments || apiData.segments.length === 0) {
      return {};
    }

    const segment = apiData.segments[0];
    const departureDate = new Date(segment.departure.scheduledDeparture);
    const arrivalDate = new Date(segment.arrival.scheduledArrival);

    return {
      images: segment.images || [],
      route: `${segment.departure.city} - ${segment.arrival.city}`,
      type: apiData.flightType === "oneWay" ? "One Way" : "Round Trip",
      rating: segment.airlineRating || 0,
      reviews: segment.reviews || 0,
      takeOffTime: departureDate.toLocaleDateString('en-US', {
        weekday: 'short',
        day: '2-digit',
        month: 'short'
      }) + ' | ' + departureDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      takeOffCode: segment.departure.cityCode,
      landingTime: arrivalDate.toLocaleDateString('en-US', {
        weekday: 'short',
        day: '2-digit',
        month: 'short'
      }) + ' | ' + arrivalDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      landingCode: segment.arrival.cityCode,
      stops: "Direct Flight", // You can modify this based on your API structure
      stopDuration: segment.duration,
      airline: segment.airlineDisplayName,
      flightType: apiData.serviceClass,
      fareType: apiData.refundable,
      cancellationFee: "As per airline policy",
      flightChange: "As per airline policy",
      seatsSequence: apiData.baggageAllowance,
      inflightFeatures: "Available",
      taxesFees: `$${apiData.tax}`
    };
  };

  // Function to map API data to sidebar format
  const mapApiDataToSidebarFormat = (apiData) => {
    if (!apiData) {
      return {};
    }

    const segment = apiData.segments[0];
    const departureDate = new Date(segment?.departure?.scheduledDeparture);
    const arrivalDate = new Date(segment?.arrival?.scheduledArrival);

    return {
      bookingData: {
        price: `$${apiData.totalPrice?.toFixed(2)}`,
        originalPrice: `$${apiData.price?.toFixed(2)}`,
        journeyDate: departureDate.toLocaleDateString('en-US'),
        journeyDay: departureDate.toLocaleDateString('en-US', { weekday: 'long' }),
        returnDate: arrivalDate.toLocaleDateString('en-US'),
        returnDay: arrivalDate.toLocaleDateString('en-US', { weekday: 'long' }),
        passengers: `${segment?.passengers?.adults || 1} Passenger${(segment?.passengers?.adults || 1) > 1 ? 's' : ''}`,
        passengerClass: apiData.serviceClass,
        views: "250 Views",
        shares: "4 Share",
        fareId: apiData.id,
        // Additional flight details for booking page
        route: `${segment?.departure?.city || ''} - ${segment?.arrival?.city || ''}`,
        airline: segment?.airlineDisplayName || '',
        flightType: apiData.flightType || 'One Way',
        flightDuration: segment?.duration || '',
        takeOff: segment?.departure?.city || '',
        landing: segment?.arrival?.city || '',
        type: `${apiData.flightType || 'One Way'} Flight`,
        rating: segment?.airlineRating || '',
        reviews: segment?.reviews || '',
        subTotal: `$${apiData.price?.toFixed(2)}`,
        discount: apiData.discountRate ? `-$${(apiData.price * apiData.discountRate).toFixed(2)}` : '',
        taxes: `$${apiData.tax?.toFixed(2)}`,
        youPay: `$${apiData.totalPrice?.toFixed(2)}`,
        // Flight card data for booking widget
        flightCardData: {
          route: `${segment?.departure?.city || ''} - ${segment?.arrival?.city || ''}`,
          airline: segment?.airlineDisplayName || '',
          flightType: apiData.flightType || 'One Way',
          flightDuration: segment?.duration || '',
          takeOff: segment?.departure?.city || '',
          landing: segment?.arrival?.city || '',
          takeOffTime: segment?.departure?.scheduledDeparture ? new Date(segment.departure.scheduledDeparture).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : '',
          landingTime: segment?.arrival?.scheduledArrival ? new Date(segment.arrival.scheduledArrival).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : '',
          takeOffCode: segment?.departure?.airportCode || '',
          landingCode: segment?.arrival?.airportCode || '',
          stops: segment?.stops || 'Non-stop',
          stopDuration: segment?.stopDuration || '',
          fareType: apiData.serviceClass || '',
          cancellationFee: apiData.refundable || 'Free',
          flightChange: 'Allowed',
          seatsSequence: `${segment?.passengers?.adults || 1} Adults, ${segment?.passengers?.children || 0} Children`,
          inflightFeatures: 'Available',
          taxesFees: `$${apiData.tax?.toFixed(2)}`,
          type: `${apiData.flightType || 'One Way'} Flight`,
          rating: segment?.airlineRating || 0,
          reviews: segment?.reviews || 0
        }
      }
    };
  };

  // Sample flight data
  const defaultFlightData = {
    images: [
      "/assets/img/flight/single-1.jpg",
      "/assets/img/flight/single-2.jpg",
      "/assets/img/flight/single-3.jpg"
    ],
    route: "New York - Los Angeles",
    type: "One Way",
    rating: 4.5,
    reviews: 35,
    takeOffTime: "Sat, 25 Oct | 07:30AM",
    takeOffCode: "JFK",
    landingTime: "Sat, 25 Oct | 09:25AM",
    landingCode: "LAX",
    stops: "1 Stop (STN)",
    stopDuration: "1h 25m",
    airline: "Delta Airlines",
    flightType: "Economy",
    fareType: "Refundable",
    cancellationFee: "$50 / Per Person",
    flightChange: "$50 / Per Person",
    seatsSequence: "$50 Extra Charge",
    inflightFeatures: "Available",
    taxesFees: "$50"
  };

  // Sample about data
  const defaultAboutData = {
    title: "About Delta Airlines",
    content: [
      "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text.",
      "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text."
    ]
  };

  // Sample inflight features data
  const defaultInflightData = {
    title: "Inflight Features",
    features: [
      { icon: Wifi, label: "WiFi", color: "bg-[#24BDC7]" },
      { icon: Tv, label: "Television", color: "bg-[#24BDC7]" },
      { icon: Music, label: "Entertainment", color: "bg-[#24BDC7]" },
      { icon: Coffee, label: "Coffee", color: "bg-[#24BDC7]" },
      { icon: AirVent, label: "Air Conditioning", color: "bg-[#24BDC7]" },
      { icon: Utensils, label: "Food", color: "bg-[#24BDC7]" },
      { icon: Gamepad2, label: "Games", color: "bg-[#24BDC7]" },
      { icon: Wine, label: "Wines", color: "bg-[#24BDC7]" },
      { icon: Armchair, label: "Comfort", color: "bg-[#24BDC7]" },
      { icon: Image, label: "Magazines", color: "bg-[#24BDC7]" }
    ]
  };

  // Sample fare rules data
  const defaultFareRulesData = {
    title: "Fare Rules For Your Flight",
    rules: [
      { icon: FileText, label: "Rules and Policies", color: "bg-[#24BDC7]" },
      { icon: RotateCcw, label: "Flight Changes", color: "bg-[#24BDC7]" },
      { icon: DollarSign, label: "Refunds", color: "bg-[#24BDC7]" },
      { icon: AlertTriangle, label: "Airline Penalties", color: "bg-[#24BDC7]" },
      { icon: X, label: "Flight Cancellation", color: "bg-[#24BDC7]" },
      { icon: Check, label: "Airline Terms Of Use", color: "bg-[#24BDC7]" }
    ]
  };

  // Sample sidebar data
  const defaultSidebarData = {
    faqData: [
      {
        question: "What Are The Charges Of Services ?",
        answer: "We denounce with righteous indignation and dislike men who are so beguiled and demoralized by the charms of pleasure of the moment, so blinded by desire odio dignissim quam."
      },
      {
        question: "How Can I Become A Member ?",
        answer: "To become a member, simply sign up on our website and choose your preferred membership plan."
      },
      {
        question: "Can I Upgrade My Plan Any Time ?",
        answer: "Yes, you can upgrade your plan at any time from your account dashboard."
      }
    ],
    contactData: {
      title: "Get A Question?",
      description: "It is a long established fact that a reader will be distracted by the readable content layout.",
      phone: "****** 4567 897",
      email: "<EMAIL>"
    },
    organizerData: {
      title: "Organized By",
      organizerName: "Roltak Travel Agency",
      memberSince: "Member Since 2025",
      avatar: "/assets/img/team/organizer-avatar.jpg"
    },
    bookingData: {
      price: "$450.00",
      originalPrice: "$500.00",
      journeyDate: "9/11/2025",
      journeyDay: "Thursday",
      returnDate: "9/12/2025",
      returnDay: "Friday",
      passengers: "2 Passenger",
      passengerClass: "Business",
      views: "250 Views",
      shares: "4 Share",
      // Default flight card data
      flightCardData: {
        route: "New York - Los Angeles",
        airline: "Delta Airlines",
        flightType: "One Way",
        flightDuration: "4h 05m",
        takeOff: "New York",
        landing: "Los Angeles",
        takeOffTime: "10:30 AM",
        landingTime: "2:35 PM",
        takeOffCode: "JFK",
        landingCode: "LAX",
        stops: "Non-stop",
        stopDuration: "",
        fareType: "Business",
        cancellationFee: "Free",
        flightChange: "Allowed",
        seatsSequence: "2 Adults",
        inflightFeatures: "Available",
        taxesFees: "$50.00",
        type: "One Way Flight",
        rating: 4.5,
        reviews: 35
      }
    },
    whyBookData: {
      title: "Why Book With Us?",
      features: [
        {
          icon: Shield,
          title: "Best Price Guarantee",
          description: "We guarantee the best prices for all our services"
        },
        {
          icon: Headphones,
          title: "24/7 Customer Care",
          description: "Round the clock customer support for your convenience"
        },
        {
          icon: MapPin,
          title: "Hand Picked Tours & Activities",
          description: "Carefully selected tours and activities for the best experience"
        },
        {
          icon: Plane,
          title: "Free Travel Insurance",
          description: "Complimentary travel insurance for your peace of mind"
        },
        {
          icon: Car,
          title: "Comfortable And Hygienic Vehicle",
          description: "Clean and comfortable vehicles for your journey"
        }
      ]
    }
  };

  // Map API data if available, otherwise use default/provided data
  const apiMappedFlightData = apiFlightData ? mapApiDataToComponentFormat(apiFlightData) : {};
  const apiMappedSidebarData = apiFlightData ? mapApiDataToSidebarFormat(apiFlightData) : {};
  const apiMappedAboutData = apiFlightData ? {
    title: `About ${apiFlightData.segments[0]?.airlineDisplayName || 'Airline'}`,
    content: defaultAboutData.content // Keep default content for now
  } : {};

  const flight = { ...defaultFlightData, ...apiMappedFlightData, ...flightData };
  const about = { ...defaultAboutData, ...apiMappedAboutData, ...aboutData };
  const inflight = { ...defaultInflightData, ...inflightData };
  const fareRules = { ...defaultFareRulesData, ...fareRulesData };
  const sidebar = { ...defaultSidebarData, ...apiMappedSidebarData, ...sidebarData };

  return (
    <div className={`min-h-screen mb-20 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content - Takes 3 columns */}
          <div className="lg:col-span-3 space-y-6">

            {/* Flight Card */}
            <FlightCard
              flightData={flight}
            />
            <hr className='border-gray-200 px-5' />

            {/* About Section */}
            <AboutSection
              title={about.title}
              content={about.content}
            />
            <hr className='border-gray-200 px-5' />

            {/* Inflight Features */}
            <InflightFeatures title={inflight.title} features={inflight.features} />
            <hr className='border-gray-200 px-5' />

            {/* Baggage Section */}
            <BaggageSection />
            <hr className='border-gray-200 px-5' />

            {/* Fare Rules */}
            <FareRules title={fareRules.title} rules={fareRules.rules} />
            <hr className='border-gray-200 px-5' />

            {/* FAQ */}
            <FAQ faqs={sidebar.faqData} />
            <hr className='border-gray-200 px-5' />

            {/* Reviews */}
            <Reviews />
            <ReviewForm />
          </div>

          {/* Sidebar - Takes 1 column */}
          <div className="lg:col-span-1">
            <Sidebar sidebarData={sidebar} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightDetailPage;
