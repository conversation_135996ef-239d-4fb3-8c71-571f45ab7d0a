"use client";
import React from 'react';
import HeaderTop from "@/components/home/<USER>";
import Header<PERSON><PERSON> from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import Breadcrumb from "@/components/ui/Breadcrumb";

const Header = ({
  backgroundImage = "/assets/img/hero/hero-1.jpg",
  height = "min-h-[110vh]",
  heroTitle = "Explore The World Together",
  heroSubtitle = "Find Awesome Flight, Hotel, Tour, Car And Packages",
  breadcrumbItems = []
}) => {
  return (
    <div
      className={`relative ${height}`}
      style={{
        backgroundImage: `url('${backgroundImage}')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-black opacity-50 z-0" />
      <HeaderTop />
      <HeaderNav />

      {/* Hero and Booking Container */}
      <div className="relative pt-4 lg:pt-8">
        {/* Show Hero with subtitle OR Hero with breadcrumb */}
        {breadcrumbItems.length > 0 ? (
          <>
            <Hero title={heroTitle} subtitle="" />
            <div className="relative z-10 flex justify-center pt-4 lg:pt-8">
              <div className="container mx-auto px-4 lg:px-8">
                <Breadcrumb items={breadcrumbItems} />
              </div>
            </div>
          </>
        ) : (
          <Hero title={heroTitle} subtitle={heroSubtitle} />
        )}
      </div>
    </div>
  );
};

export default Header;
