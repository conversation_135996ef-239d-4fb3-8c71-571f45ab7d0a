import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

// Add missing icon imports
import { Menu, X, ChevronDown } from 'react-feather';
const navItems = [
  {
    label: "Home",
    dropdown: [],
    path: "/",
  },
  {
    label: "Flight",
    dropdown: [
      "Flight List",
      "Flight Details",
      "Flight Booking",
    ],
  },
  {
    label: "Hotel",
    dropdown: [
      "Luxury Hotels",
      "Budget Hotels",
      "Resorts",
      "Hotel Deals",
    ],
  },
  { label: "About", dropdown: [], path: "/about" },
  { label: "Contact", dropdown: [], path: "/contact" },
];

const HeaderNav = () => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [expanded, setExpanded] = useState({});
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleExpanded = (label) => {
    setExpanded(prev => ({ ...prev, [label]: !prev[label] }));
  };

  const handleMouseEnter = (label) => {
    setActiveDropdown(label);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  const handleItemClick = (item) => {
    if (item.dropdown.length === 0) {
      // Handle direct navigation items
      if (item.path) {
        router.push(item.path);
      }
      setIsOpen(false);
    } else {
      toggleExpanded(item.label);
    }
  };

  const handleHomeClick = () => {
    router.push('/');
    setIsOpen(false);
  };

  const handleLogoClick = () => {
    router.push('/');
  };

  const handleDropdownItemClick = (item, subItem) => {
    // Handle specific dropdown item clicks
    if (item.label === "Flight" && subItem === "Flight List") {
      router.push('/flight-list');
      setIsOpen(false);
    } else if (item.label === "Flight" && subItem === "Flight Details") {
      router.push('/flight-details');
      setIsOpen(false);
    } else if (item.label === "Flight" && subItem === "Flight Booking") {
      router.push('/flight-booking');
      setIsOpen(false);
    }
    // Add more specific routing logic as needed
  };

  return (
    <>
      <nav className={`sticky top-0 w-full px-4 lg:px-10 py-3 flex flex-col lg:flex-row items-center z-10 ${isScrolled ? 'bg-white text-[#24bdc7] shadow-lg' : 'text-white'}`}>
        <div className="relative z-10 flex flex-col lg:flex-row items-center w-full">
          {/* Logo and Hamburger on same line on mobile */}
          <div className="flex flex-row gap-2 items-center w-full lg:w-auto">
            <button className={`lg:hidden p-0 m-0 ${isScrolled ? 'text-[#0C2C7A]' : 'text-white'}`} onClick={() => setIsOpen(true)}>
              <Menu size={24} />
            </button>
            <div className="flex-1 flex m-0 p-0 cursor-pointer" onClick={handleLogoClick}>
              <Image src={isScrolled ? "/assets/img/logo/logo-dark.png" : "/assets/img/logo/logo.png"} alt="Logo" width={160} height={160} className="m-0" />
            </div>
          </div>
          {/* Navigation */}
          <ul className="hidden lg:flex flex-col lg:flex-row gap-4 lg:gap-6 items-center mt-2 lg:ml-115">
            {navItems.map((item) => (
              <li 
                key={item.label} 
                className="relative pb-2"
                onMouseEnter={() => handleMouseEnter(item.label)}
                onMouseLeave={handleMouseLeave}
              >
                <button className={`flex items-center gap-1 font-semibold ${isScrolled ? "text-[#0C2C7A]" : "text-white"} hover:text-[#24bdc7]`} onClick={() => handleItemClick(item)}>
                  {item.label}
                  {item.dropdown.length > 0 && <ChevronDown size={16} />}
                </button>
                {item.dropdown.length > 0 && (
                  <ul 
                    className={`absolute left-0 top-full bg-white text-[#0C2C7A] w-56 py-2 z-[60] shadow-lg border border-gray-200 rounded-lg transition-all duration-300 ${
                      activeDropdown === item.label ? 'opacity-100 visible' : 'opacity-0 invisible'
                    }`}
                    onMouseEnter={() => handleMouseEnter(item.label)}
                    onMouseLeave={handleMouseLeave}
                  >
                    {item.dropdown.map((sub) => (
                      <li
                        key={sub}
                        className="px-4 py-2 hover:bg-[#24bdc7] hover:text-white cursor-pointer"
                        onMouseEnter={() => handleMouseEnter(item.label)}
                        onMouseLeave={handleMouseLeave}
                        onClick={() => handleDropdownItemClick(item, sub)}
                      >
                        {sub}
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </div>
        <button className={`hidden xl:block bg-[#24bdc7] text-white px-4  py-2  rounded-2xl font-semibold whitespace-nowrap text-sm  mt-4 lg:mt-0`}>
          Become An Expert
        </button>
      </nav>
      {/* Drawer for Mobile/Tablet */}
      <div className={`fixed inset-0 backdrop-blur-sm z-50 ${isOpen ? 'block' : 'hidden'}`} onClick={() => setIsOpen(false)}>
        <div className="fixed top-0 left-0 w-80 h-full bg-[#0a2233] text-white p-4 overflow-y-auto" onClick={(e) => e.stopPropagation()}>
          <div className="flex justify-between items-center mb-4">
            <div className="cursor-pointer" onClick={handleLogoClick}>
              <Image src={isScrolled ? "/assets/img/logo/logo-dark.png" : "/assets/img/logo/logo.png"} alt="Logo" width={160} height={160} />
            </div>
            <button onClick={() => setIsOpen(false)} className="text-white hover:text-[#24bdc7]"><X size={24} /></button>
          </div>
          <ul className="flex flex-col gap-4">
            {navItems.map((item, idx) => (
              <li key={item.label} className="">
                <button className="flex items-center justify-between gap-1 font-semibold hover:text-[#24bdc7] w-full text-left" onClick={() => handleItemClick(item)}>
                  {item.label}
                  {item.dropdown.length > 0 && <ChevronDown size={16} className={`transition-transform ${expanded[item.label] ? 'rotate-180' : ''}`} />}
                </button>
                {item.dropdown.length > 0 && (
                  <ul className={`ml-4 mt-2 ${expanded[item.label] ? 'block' : 'hidden'}`}>
                    {item.dropdown.map((sub, i) => (
                      <li key={sub} className="py-1 hover:text-[#24bdc7] cursor-pointer" onClick={() => handleDropdownItemClick(item, sub)}>
                        {sub}
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </>
  );
};

export default HeaderNav;
