"use client";
import React from 'react';
import { useParams } from 'next/navigation';
import FlightDetailPage from '@/components/bookingDetail/FlightDetailPage';
import { useFlightDetail } from '@/hooks/useFlightDetail';

/**
 * Flight Detail Page Component
 * Route: /flight-detail/[id]
 * Example: /flight-detail/fare-0001
 */
const FlightDetailPageRoute = () => {
  const params = useParams();
  const flightId = params?.id;

  // Fetch flight data using the custom hook
  const { data: flightData, loading, error, refetch } = useFlightDetail(flightId);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#24BDC7] mx-auto"></div>
          <p className="mt-4 text-gray-600 text-lg">Loading flight details...</p>
          <p className="text-sm text-gray-500">Flight ID: {flightId}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong className="font-bold">Error!</strong>
            <span className="block sm:inline"> {error}</span>
          </div>
          <button 
            onClick={refetch}
            className="bg-[#24BDC7] hover:bg-[#1ea5ae] text-white font-bold py-2 px-4 rounded transition-colors"
          >
            Try Again
          </button>
          <p className="text-sm text-gray-500 mt-2">Flight ID: {flightId}</p>
        </div>
      </div>
    );
  }

  // No data state
  if (!flightData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-gray-600 text-lg">No flight data found</p>
          <p className="text-sm text-gray-500">Flight ID: {flightId}</p>
        </div>
      </div>
    );
  }

  // Success state - render the flight detail page
  return (
    <FlightDetailPage 
      apiFlightData={flightData}
      className="bg-gray-50"
    />
  );
};

export default FlightDetailPageRoute;
