// components/Booking.js
import React, { useState ,useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { setBookingData } from '../../redux/slices/bookingSlice';
import TabButton from './BookingComponents/TabButton';
import TripTypeSelector from './BookingComponents/TripTypeSelector';
import LocationSelector from './BookingComponents/LocationSelector';
import SwapButton from './BookingComponents/SwapButton';
import DateSelector from './BookingComponents/DateSelector';
import DepartureDateSelector from './BookingComponents/DepartureDateSelector';
import ReturnDateSelector from './BookingComponents/ReturnDateSelector';
import PassengerSelector from './BookingComponents/PassengerSelector';
import FlightActionButton from './BookingComponents/FlightActionButton';
import HotelDestinationSelector from './BookingComponents/HotelDestinationSelector';
import CheckInDateSelector from './BookingComponents/CheckInDateSelector';
import CheckOutDateSelector from './BookingComponents/CheckOutDateSelector';
import HotelRoomSelector from './BookingComponents/HotelRoomSelector';
import HotelTripTypeSelector from './BookingComponents/HotelTripTypeSelector';
import { bookingTabs } from './BookingComponents/tabConfig';
import hotelDestinationsData from '../../app/data/hotelDestinationsData.json';
import bookingConfigData from '../../app/data/bookingConfigData.json';

// Configuration for booking layouts
const bookingLayouts = {
  flights: {
    selectors: ['from', 'to', 'date', 'passenger'],
    gridCols: 12
  },
  hotels: {
    selectors: ['destination', 'date', 'room'],
    gridCols: 12
  },
  multiCity: {
    selectors: ['from', 'to', 'date', 'action'], // action can be passenger, addFlight, or remove
    gridCols: 12
  },
  hotelMultiCity: {
    selectors: ['destination', 'date', 'action'], // action can be room, addHotel, or remove
    gridCols: 12
  }
};


const Booking = ({ className }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const pathname = usePathname();
  const bookingData = useSelector((state) => state.booking);
  
  const [activeTab, setActiveTab] = useState('flights');
  const [tripType, setTripType] = useState('oneWay');
  const [hotelTripType, setHotelTripType] = useState('single');
  
  // Multi-city flight segments
  const [flightSegments, setFlightSegments] = useState([
    {
      id: 1,
      fromLocation: {
        city: bookingConfigData.defaultLocations.from.city,
        airport: bookingConfigData.defaultLocations.from.airport,
        cityCode: bookingConfigData.defaultLocations.from.cityCode,
        countryCode: bookingConfigData.defaultLocations.from.countryCode,
        countryName: bookingConfigData.defaultLocations.from.countryName
      },
      toLocation: {
        city: bookingConfigData.defaultLocations.to.city,
        airport: bookingConfigData.defaultLocations.to.airport,
        cityCode: bookingConfigData.defaultLocations.to.cityCode,
        countryCode: bookingConfigData.defaultLocations.to.countryCode,
        countryName: bookingConfigData.defaultLocations.to.countryName
      },
      departureDate: new Date()
    },
    {
      id: 2,
      fromLocation: {
        city: bookingConfigData.defaultLocations.to.city,
        airport: bookingConfigData.defaultLocations.to.airport,
        cityCode: bookingConfigData.defaultLocations.to.cityCode,
        countryCode: bookingConfigData.defaultLocations.to.countryCode,
        countryName: bookingConfigData.defaultLocations.to.countryName
      },
      toLocation: {
        city: 'London',
        airport: 'LHR - London Heathrow Airport',
        cityCode: 'LHR',
        countryCode: 'GB',
        countryName: 'United Kingdom'
      },
      departureDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days later
    }
  ]);
  
  // Single trip states (for one-way and round-way)
  const [fromLocation, setFromLocation] = useState({
    city: bookingConfigData.defaultLocations.from.city,
    airport: bookingConfigData.defaultLocations.from.airport,
    cityCode: bookingConfigData.defaultLocations.from.cityCode,
    countryCode: bookingConfigData.defaultLocations.from.countryCode,
    countryName: bookingConfigData.defaultLocations.from.countryName
  });
  const [toLocation, setToLocation] = useState({
    city: bookingConfigData.defaultLocations.to.city,
    airport: bookingConfigData.defaultLocations.to.airport,
    cityCode: bookingConfigData.defaultLocations.to.cityCode,
    countryCode: bookingConfigData.defaultLocations.to.countryCode,
    countryName: bookingConfigData.defaultLocations.to.countryName
  });
  const [departureDate, setDepartureDate] = useState(() => new Date());
  const [returnDate, setReturnDate] = useState(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
  });
  const [passengers, setPassengers] = useState(bookingConfigData.defaultPassengers);
  const [travelClass, setTravelClass] = useState(bookingConfigData.defaultTravelClass);

  // Multi-city hotel segments
  const [hotelSegments, setHotelSegments] = useState([
    {
      id: 1,
      destination: {
        city: bookingConfigData.defaultHotelDestination.city,
        country: bookingConfigData.defaultHotelDestination.country,
        attractions: bookingConfigData.defaultHotelDestination.attractions
      },
      checkInDate: new Date(),
      checkOutDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days later
      rooms: [{ adults: 2, children: 0, roomType: bookingConfigData.defaultRoomType }]
    },
    {
      id: 2,
      destination: {
        city: hotelDestinationsData.find(dest => dest.city === 'Los Angeles')?.city || 'Los Angeles',
        country: hotelDestinationsData.find(dest => dest.city === 'Los Angeles')?.country || 'United States',
        attractions: hotelDestinationsData.find(dest => dest.city === 'Los Angeles')?.attractions || 'Hollywood, Santa Monica Pier'
      },
      checkInDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days later
      checkOutDate: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000), // 9 days later
      rooms: [{ adults: 2, children: 0, roomType: bookingConfigData.defaultRoomType }]
    }
  ]);

  // Hotel booking states
  const [hotelDestination, setHotelDestination] = useState({
    city: bookingConfigData.defaultHotelDestination.city,
    country: bookingConfigData.defaultHotelDestination.country,
    attractions: bookingConfigData.defaultHotelDestination.attractions
  });
  const [checkInDate, setCheckInDate] = useState(() => new Date());
  const [checkOutDate, setCheckOutDate] = useState(() => {
    const checkOut = new Date();
    checkOut.setDate(checkOut.getDate() + 1);
    return checkOut;
  });
  const [hotelRooms, setHotelRooms] = useState([
    { adults: bookingConfigData.defaultPassengers.adults, children: bookingConfigData.defaultPassengers.children, roomType: bookingConfigData.defaultRoomType }
  ]);

  // Populate form with Redux data when component mounts or data changes
  useEffect(() => {
    if (bookingData && Object.keys(bookingData).length > 0) {
      // Set active tab
      if (bookingData.activeTab) {
        setActiveTab(bookingData.activeTab);
      }

      // Set trip types
      if (bookingData.tripType) {
        setTripType(bookingData.tripType);
      }
      if (bookingData.hotelTripType) {
        setHotelTripType(bookingData.hotelTripType);
      }

      // Set locations for single trip
      if (bookingData.fromLocation) {
        setFromLocation(bookingData.fromLocation);
      }
      if (bookingData.toLocation) {
        setToLocation(bookingData.toLocation);
      }

      // Set dates - convert ISO strings back to Date objects
      if (bookingData.departureDate) {
        const departureDate = typeof bookingData.departureDate === 'string' 
          ? new Date(bookingData.departureDate) 
          : bookingData.departureDate;
        setDepartureDate(departureDate);
      }
      if (bookingData.returnDate) {
        const returnDate = typeof bookingData.returnDate === 'string' 
          ? new Date(bookingData.returnDate) 
          : bookingData.returnDate;
        setReturnDate(returnDate);
      }

      // Set passengers and travel class
      if (bookingData.passengers) {
        setPassengers(bookingData.passengers);
      }
      if (bookingData.travelClass) {
        setTravelClass(bookingData.travelClass);
      }

      // Set flight segments for multi-city
      if (bookingData.flightSegments && bookingData.flightSegments.length > 0) {
        const segments = bookingData.flightSegments.map(segment => ({
          ...segment,
          departureDate: typeof segment.departureDate === 'string' 
            ? new Date(segment.departureDate) 
            : segment.departureDate
        }));
        setFlightSegments(segments);
      }

      // Set hotel data
      if (bookingData.hotelDestination) {
        setHotelDestination(bookingData.hotelDestination);
      }
      if (bookingData.checkInDate) {
        const checkInDate = typeof bookingData.checkInDate === 'string' 
          ? new Date(bookingData.checkInDate) 
          : bookingData.checkInDate;
        setCheckInDate(checkInDate);
      }
      if (bookingData.checkOutDate) {
        const checkOutDate = typeof bookingData.checkOutDate === 'string' 
          ? new Date(bookingData.checkOutDate) 
          : bookingData.checkOutDate;
        setCheckOutDate(checkOutDate);
      }
      if (bookingData.hotelRooms) {
        setHotelRooms(bookingData.hotelRooms);
      }

      // Set hotel segments
      if (bookingData.hotelSegments && bookingData.hotelSegments.length > 0) {
        setHotelSegments(bookingData.hotelSegments);
      }
    }
  }, [bookingData]);

  // Handle passenger changes
  const handlePassengerChange = (newPassengers, newTravelClass) => {
    setPassengers(newPassengers);
    setTravelClass(newTravelClass);
  };

  // Handle location changes
  const handleFromLocationChange = (newLocation) => {
    setFromLocation(newLocation);
  };

  const handleToLocationChange = (newLocation) => {
    setToLocation(newLocation);
  };

  // Handle swap locations
  const handleSwapLocations = () => {
    const temp = fromLocation;
    setFromLocation(toLocation);
    setToLocation(temp);
  };

  // Handle date changes
  const handleDateChange = (dateType, newDate) => {
    if (dateType === 'departure') {
      setDepartureDate(newDate);
      // Auto-adjust return date if it's before departure
      if (returnDate <= newDate) {
        const nextDay = new Date(newDate);
        nextDay.setDate(newDate.getDate() + 1);
        setReturnDate(nextDay);
      }
    } else if (dateType === 'return') {
      setReturnDate(newDate);
    }
  };

  // Hotel booking handlers
  const handleHotelDestinationChange = (newDestination) => {
    setHotelDestination(newDestination);
  };

  const handleHotelDateChange = (dateType, newDate) => {
    if (dateType === 'checkIn') {
      setCheckInDate(newDate);
      // Auto-adjust check out date if it's before or same as check in
      if (checkOutDate <= newDate) {
        const nextDay = new Date(newDate);
        nextDay.setDate(newDate.getDate() + 1);
        setCheckOutDate(nextDay);
      }
    } else if (dateType === 'checkOut') {
      setCheckOutDate(newDate);
    }
  };

  const handleHotelRoomsChange = (newRooms) => {
    setHotelRooms(newRooms);
  };

  // Multi-city hotel handlers
  const addHotelSegment = () => {
    if (hotelSegments.length >= 5) return; // Maximum 5 hotel segments
    const newSegment = {
      id: Date.now(),
      destination: {
        city: '',
        country: '',
        attractions: ''
      },
      checkInDate: new Date(),
      checkOutDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days later
      rooms: [{ adults: 2, children: 0, roomType: 'Double Room' }]
    };
    setHotelSegments([...hotelSegments, newSegment]);
  };

  const removeHotelSegment = (segmentId) => {
    if (hotelSegments.length > 1) {
      setHotelSegments(hotelSegments.filter(segment => segment.id !== segmentId));
    }
  };

  const updateHotelSegment = (segmentId, field, value) => {
    setHotelSegments(hotelSegments.map(segment => 
      segment.id === segmentId ? { ...segment, [field]: value } : segment
    ));
  };

  const handleHotelSegmentDestinationChange = (segmentId, newDestination) => {
    updateHotelSegment(segmentId, 'destination', newDestination);
  };

  const handleHotelSegmentDateChange = (segmentId, dateType, newDate) => {
    updateHotelSegment(segmentId, dateType, newDate);
  };

  const handleHotelSegmentRoomsChange = (segmentId, newRooms) => {
    updateHotelSegment(segmentId, 'rooms', newRooms);
  };

  // Multi-city handlers
  const addFlightSegment = () => {
    const newSegment = {
      id: Date.now(),
      fromLocation: {
        city: '',
        airport: '',
        cityCode: '',
        countryCode: '',
        countryName: ''
      },
      toLocation: {
        city: '',
        airport: '',
        cityCode: '',
        countryCode: '',
        countryName: ''
      },
      departureDate: new Date()
    };
    setFlightSegments([...flightSegments, newSegment]);
  };

  const removeFlightSegment = (segmentId) => {
    if (flightSegments.length > 1) {
      setFlightSegments(flightSegments.filter(segment => segment.id !== segmentId));
    }
  };

  const updateFlightSegment = (segmentId, field, value) => {
    setFlightSegments(flightSegments.map(segment => 
      segment.id === segmentId ? { ...segment, [field]: value } : segment
    ));
  };

  const handleSegmentLocationChange = (segmentId, locationType, newLocation) => {
    updateFlightSegment(segmentId, locationType, newLocation);
  };

  const handleSegmentDateChange = (segmentId, dateType, newDate) => {
    updateFlightSegment(segmentId, 'departureDate', newDate);
  };

  const swapSegmentLocations = (segmentId) => {
    setFlightSegments(flightSegments.map(segment => {
      if (segment.id === segmentId) {
        return {
          ...segment,
          fromLocation: segment.toLocation,
          toLocation: segment.fromLocation
        };
      }
      return segment;
    }));
  };

  // Handle search button click
  const handleSearch = () => {
    // Helper function to serialize dates
    const serializeDate = (date) => {
      if (date instanceof Date) {
        return date.toISOString();
      }
      return date;
    };

    // Helper function to serialize flight segments
    const serializeFlightSegments = (segments) => {
      return segments.map(segment => ({
        ...segment,
        departureDate: serializeDate(segment.departureDate)
      }));
    };

    // Helper function to serialize hotel segments
    const serializeHotelSegments = (segments) => {
      return segments.map(segment => ({
        ...segment,
        checkInDate: serializeDate(segment.checkInDate),
        checkOutDate: serializeDate(segment.checkOutDate)
      }));
    };

    const bookingData = {
      activeTab,
      tripType,
      hotelTripType,
      // Single trip data - serialize dates
      fromLocation,
      toLocation,
      departureDate: serializeDate(departureDate),
      returnDate: serializeDate(returnDate),
      passengers,
      travelClass,
      // Multi-city flight segments - serialize dates
      flightSegments: serializeFlightSegments(flightSegments),
      // Hotel data - serialize dates
      hotelDestination,
      checkInDate: serializeDate(checkInDate),
      checkOutDate: serializeDate(checkOutDate),
      hotelRooms,
      // Multi-city hotel segments - serialize dates
      hotelSegments: serializeHotelSegments(hotelSegments)
    };

    // Dispatch to Redux
    dispatch(setBookingData(bookingData));

    // Navigate to flight details page
    if (activeTab === 'flights') {
      router.push('/flight-list');
    } else if (activeTab === 'hotels') {
      // You can add hotel details page navigation here
      console.log('Hotel search not implemented yet');
    } else {
      console.log('Other tabs not implemented yet');
    }
  };

  return (
    <div className={`w-full px-6 py-4 ${className}`}>
      {/* Desktop Tabs - positioned outside the main container */}
      <div className="hidden lg:flex flex-col lg:flex-row gap-2 lg:gap-[25rem] xl:gap-[35rem] justify-center items-center relative z-10">
        <div className='flex gap-0.5 lg:gap-1 rounded-full border-white border-2 lg:border-2 max-w-full bg-white p-0.5 lg:p-1'>
        {bookingTabs.map((tab) => (
          <TabButton
            key={tab.id}
            id={tab.id}
            label={tab.label}
            icon={tab.icon}
            isActive={activeTab === tab.id}
            onClick={setActiveTab}
          />
        ))}
        </div>
        <div className="lg:ml-2 lg:pl-2">
          <button 
            className="flex items-center gap-1 px-4 lg:px-4 py-1.5 bg-[#24BDC7] text-white rounded-full font-semibold border-2 lg:border-4 text-xs lg:text-sm"
            onClick={handleSearch}
          >
            <svg className="w-4 h-4 lg:w-5 lg:h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
            </svg>
            {activeTab === 'flights' ? (pathname.includes('/flight-list') ? 'Update Search' : 'Search') : 
             activeTab === 'hotels' ? (hotelTripType === 'multiCity' ? (pathname.includes('/flight-list') ? 'Update Search' : 'Search') : (pathname.includes('/flight-list') ? 'Update Search' : 'Search')) : 
             (pathname.includes('/flight-list') ? 'Update Search' : 'Search')}
          </button>
        </div>
      </div>

      {/* Main Booking Container */}
      <div className="p-4 lg:pt-6 lg:p-3 mt-[-10px] lg:mt-[-20px] bg-white rounded-[0.5rem] lg:rounded-[1rem] shadow-xs relative z-0">
        {/* Mobile Tabs - positioned inside the main container */}
        <div className="flex flex-col gap-0.5 justify-center items-center relative z-10 lg:hidden mb-1">
          <div className='flex flex-wrap gap-1 justify-center rounded-full border-white border-2 max-w-full bg-white p-0.5'>
          {bookingTabs.map((tab) => (
            <TabButton
              key={tab.id}
              id={tab.id}
              label={tab.label}
              icon={tab.icon}
              isActive={activeTab === tab.id}
              onClick={setActiveTab}
            />
          ))}
          </div>
          <div className="flex justify-center">
            <button 
              className="flex items-center gap-0.5 px-2 py-0.5 bg-[#24BDC7] text-white rounded-full font-semibold border-1 text-xs"
              onClick={handleSearch}
            >
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
              </svg>
              {activeTab === 'flights' ? (pathname.includes('/flight-list') ? 'Update Search' : 'Search') : 
               activeTab === 'hotels' ? (hotelTripType === 'multiCity' ? (pathname.includes('/flight-list') ? 'Update Search' : 'Search') : (pathname.includes('/flight-list') ? 'Update Search' : 'Search')) : 
               (pathname.includes('/flight-list') ? 'Update Search' : 'Search')}
            </button>
          </div>
        </div>

        {/* Conditional Content Based on Active Tab */}
        {activeTab === 'flights' ? (
          <>
            {/* Trip Type for Flights */}
            <TripTypeSelector 
              tripType={tripType}
              onChange={setTripType}
            />

            {/* Flight Search Form */}
            {tripType === 'multiCity' ? (
              /* Multi-City Layout */
              <div className="space-y-2">
                {flightSegments.map((segment, index) => (
                  <div key={segment.id} className="bg-white rounded-lg p-0.5 space-y-0.5">
                    {/* Segment Header */}
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-semibold text-[#0C2C7A]">Flight {index + 1}</h3>
                    </div>

                    {/* Unified Layout - Stacked on Mobile, Row on Desktop */}
                    <div className="flex flex-col lg:flex-row gap-2 relative">
                      {/* From Location */}
                      <div className="w-full lg:flex-1">
                        <LocationSelector
                          label="From"
                          location={segment.fromLocation}
                          onLocationChange={(location) => handleSegmentLocationChange(segment.id, 'fromLocation', location)}
                        />
                      </div>

                      {/* To Location */}
                      <div className="w-full lg:flex-1">
                        <LocationSelector
                          label="To"
                          location={segment.toLocation}
                          onLocationChange={(location) => handleSegmentLocationChange(segment.id, 'toLocation', location)}
                        />
                      </div>

                      {/* Departure Date */}
                      <div className="w-full lg:flex-1">
                        <DateSelector
                          tripType="oneWay"
                          departureDate={segment.departureDate}
                          returnDate={null}
                          onDateChange={(dateType, date) => handleSegmentDateChange(segment.id, dateType, date)}
                        />
                      </div>

                      {/* Passenger Selector or Add Flight Button or Remove Button */}
                      <div className="w-full lg:flex-1">
                        {index === 0 ? (
                          /* Passenger Selector for first segment */
                          <PassengerSelector
                            passengers={passengers}
                            travelClass={travelClass}
                            onPassengerChange={handlePassengerChange}
                            isMultiCity={true}
                          />
                        ) : index === 1 ? (
                          /* Add Flight Button for second segment */
                          <FlightActionButton
                            action="add"
                            onAction={addFlightSegment}
                          />
                        ) : (
                          /* Remove Button for additional segments */
                          <FlightActionButton
                            action="remove"
                            onAction={removeFlightSegment}
                            segmentId={segment.id}
                          />
                        )}
                      </div>

                      {/* Swap Button - Positioned between From and To */}
                      <div className="absolute left-[50%] lg:left-[25%] top-[25%] lg:top-[50%] transform -translate-x-1/2 -translate-y-1/2 z-10">
                        <SwapButton onSwap={() => swapSegmentLocations(segment.id)} />
                      </div>
                    </div>

                    {/* Desktop/Laptop Layout - All in Same Line */}
                    <div className="hidden">
                      <div className="flex flex-row gap-2 relative">
                        {/* From Location */}
                        <div className="flex-1">
                          <LocationSelector
                            label="From"
                            location={segment.fromLocation}
                            onLocationChange={(location) => handleSegmentLocationChange(segment.id, 'fromLocation', location)}
                          />
                        </div>

                        {/* To Location */}
                        <div className="flex-1">
                          <LocationSelector
                            label="To"
                            location={segment.toLocation}
                            onLocationChange={(location) => handleSegmentLocationChange(segment.id, 'toLocation', location)}
                          />
                        </div>

                        {/* Departure Date */}
                        <div className="flex-1">
                          <DateSelector
                            tripType="oneWay"
                            departureDate={segment.departureDate}
                            returnDate={null}
                            onDateChange={(dateType, date) => handleSegmentDateChange(segment.id, dateType, date)}
                          />
                        </div>

                        {/* Passenger Selector or Add Flight Button or Remove Button */}
                        <div className="flex-1">
                          {index === 0 ? (
                            /* Passenger Selector for first segment */
                            <PassengerSelector
                              passengers={passengers}
                              travelClass={travelClass}
                              onPassengerChange={handlePassengerChange}
                              isMultiCity={true}
                            />
                          ) : index === 1 ? (
                            /* Add Flight Button for second segment */
                            <FlightActionButton
                              action="add"
                              onAction={addFlightSegment}
                            />
                          ) : (
                            /* Remove Button for additional segments */
                            <FlightActionButton
                              action="remove"
                              onAction={removeFlightSegment}
                              segmentId={segment.id}
                            />
                          )}
                        </div>

                        {/* Swap Button - Positioned between From and To */}
                        <div className="absolute left-[25%] top-[50%] transform -translate-x-1/2 -translate-y-1/2 z-10">
                          <SwapButton onSwap={() => swapSegmentLocations(segment.id)} />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              /* Single Trip Layout (One Way / Round Way) */
              <div className="space-y-1">
                {/* Unified Layout - Stacked on Mobile, Row on Desktop */}
                <div className="flex flex-col lg:flex-row gap-2 relative">
                  {/* From Location */}
                  <div className="w-full lg:flex-1">
                    <LocationSelector
                      label="From"
                      location={fromLocation}
                      onLocationChange={handleFromLocationChange}
                    />
                  </div>

                  {/* To Location */}
                  <div className="w-full lg:flex-1">
                    <LocationSelector
                      label="To"
                      location={toLocation}
                      onLocationChange={handleToLocationChange}
                    />
                  </div>

                  {/* Date Selectors - Conditional based on trip type */}
                  {tripType === 'roundTrip' ? (
                    <>
                      {/* Departure Date */}
                      <div className="w-full lg:flex-1">
                        <DepartureDateSelector
                          departureDate={departureDate}
                          onDateChange={handleDateChange}
                        />
                      </div>

                      {/* Return Date */}
                      <div className="w-full lg:flex-1">
                        <ReturnDateSelector
                          departureDate={departureDate}
                          returnDate={returnDate}
                          onDateChange={handleDateChange}
                        />
                      </div>
                    </>
                  ) : (
                    /* One Way Date */
                    <div className="w-full lg:flex-1">
                      <DateSelector
                        tripType={tripType}
                        departureDate={departureDate}
                        returnDate={returnDate}
                        onDateChange={handleDateChange}
                      />
                    </div>
                  )}

                  {/* Passenger & Class */}
                  <div className="w-full lg:flex-1">
                    <PassengerSelector 
                      passengers={passengers}
                      travelClass={travelClass}
                      onPassengerChange={handlePassengerChange}
                      isMultiCity={false}
                      segmentIndex={0}
                    />
                  </div>

                  {/* Swap Button - Positioned between From and To */}
                  <div className={`absolute ${tripType === 'roundTrip' ? 'lg:left-[20%] top-[20%] ' : 'lg:left-[25%] top-[25%]'} left-[50%] lg:top-[50%] transform -translate-x-1/2 -translate-y-1/2 z-10`}>
                    <SwapButton onSwap={handleSwapLocations} />
                  </div>
                </div>

                {/* Desktop/Laptop Layout - All in Same Line */}
                <div className="hidden">
                  <div className="flex flex-row gap-2 relative">
                    {/* From Location */}
                    <div className="flex-1">
                      <LocationSelector
                        label="From"
                        location={fromLocation}
                        onLocationChange={handleFromLocationChange}
                      />
                    </div>

                    {/* To Location */}
                    <div className="flex-1">
                      <LocationSelector
                        label="To"
                        location={toLocation}
                        onLocationChange={handleToLocationChange}
                      />
                    </div>

                    {/* Date Selectors - Conditional based on trip type */}
                    {tripType === 'roundTrip' ? (
                      <>
                        {/* Departure Date */}
                        <div className="flex-1">
                          <DepartureDateSelector
                            departureDate={departureDate}
                            onDateChange={handleDateChange}
                          />
                        </div>

                        {/* Return Date */}
                        <div className="flex-1">
                          <ReturnDateSelector
                            departureDate={departureDate}
                            returnDate={returnDate}
                            onDateChange={handleDateChange}
                          />
                        </div>
                      </>
                    ) : (
                      /* One Way Date */
                      <div className="flex-1">
                        <DateSelector
                          tripType={tripType}
                          departureDate={departureDate}
                          returnDate={returnDate}
                          onDateChange={handleDateChange}
                        />
                      </div>
                    )}

                    {/* Passenger & Class */}
                    <div className="flex-1">
                      <PassengerSelector 
                        passengers={passengers}
                        travelClass={travelClass}
                        onPassengerChange={handlePassengerChange}
                        isMultiCity={false}
                        segmentIndex={0}
                      />
                    </div>

                    {/* Swap Button - Absolute positioned between From and To */}
                    <div className={`absolute ${tripType === 'roundTrip' ? 'left-[10%]' : 'left-[12.5%]'} top-[50%] transform -translate-x-1/2 -translate-y-1/2 z-10`}>
                      <SwapButton onSwap={handleSwapLocations} />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : activeTab === 'hotels' ? (
          <>
            {/* Hotel Trip Type for Hotels */}
            <HotelTripTypeSelector
              hotelTripType={hotelTripType}
              onChange={setHotelTripType}
            />

            {/* Hotel Search Form */}
            {hotelTripType === 'multiCity' ? (
              /* Multi-City Hotel Layout */
              <div className="space-y-2">
                {hotelSegments.map((segment, index) => (
                  <div key={segment.id} className="bg-white rounded-lg p-0.5 space-y-0.5">
                    {/* Segment Header */}
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-semibold text-[#0C2C7A]">Hotel {index + 1}</h3>
                    </div>

                    {/* Unified Layout - Stacked on Mobile, Row on Desktop */}
                    <div className="flex flex-col lg:flex-row gap-2">
                      {/* Destination */}
                      <div className="w-full lg:flex-1">
                        <HotelDestinationSelector
                          destination={segment.destination}
                          onDestinationChange={(destination) => handleHotelSegmentDestinationChange(segment.id, destination)}
                        />
                      </div>

                      {/* Check In Date */}
                      <div className="w-full lg:flex-1">
                        <CheckInDateSelector
                          checkInDate={segment.checkInDate}
                          onDateChange={(dateType, date) => handleHotelSegmentDateChange(segment.id, dateType, date)}
                        />
                      </div>

                      {/* Check Out Date */}
                      <div className="w-full lg:flex-1">
                        <CheckOutDateSelector
                          checkInDate={segment.checkInDate}
                          checkOutDate={segment.checkOutDate}
                          onDateChange={(dateType, date) => handleHotelSegmentDateChange(segment.id, dateType, date)}
                        />
                      </div>

                      {/* Rooms & Guests or Add Hotel Button or Remove Button */}
                      <div className="w-full lg:flex-1">
                        {index === 0 ? (
                          /* Rooms & Guests for first segment */
                          <HotelRoomSelector
                            rooms={segment.rooms}
                            onRoomsChange={(rooms) => handleHotelSegmentRoomsChange(segment.id, rooms)}
                          />
                        ) : index === 1 ? (
                          /* Add Hotel Button for second segment */
                          <FlightActionButton
                            action="add"
                            onAction={addHotelSegment}
                            type="hotel"
                          />
                        ) : (
                          /* Remove Button for additional segments */
                          <FlightActionButton
                            action="remove"
                            onAction={removeHotelSegment}
                            segmentId={segment.id}
                            type="hotel"
                          />
                        )}
                      </div>
                    </div>

                    {/* Desktop/Laptop Layout - All in Same Line */}
                    <div className="hidden">
                      <div className="flex flex-row gap-2 relative">
                        {/* Destination */}
                        <div className="flex-1">
                          <HotelDestinationSelector
                            destination={segment.destination}
                            onDestinationChange={(destination) => handleHotelSegmentDestinationChange(segment.id, destination)}
                          />
                        </div>

                        {/* Check In Date */}
                        <div className="flex-1">
                          <CheckInDateSelector
                            checkInDate={segment.checkInDate}
                            onDateChange={(dateType, date) => handleHotelSegmentDateChange(segment.id, dateType, date)}
                          />
                        </div>

                        {/* Check Out Date */}
                        <div className="flex-1">
                          <CheckOutDateSelector
                            checkInDate={segment.checkInDate}
                            checkOutDate={segment.checkOutDate}
                            onDateChange={(dateType, date) => handleHotelSegmentDateChange(segment.id, dateType, date)}
                          />
                        </div>

                        {/* Rooms & Guests or Add Hotel Button or Remove Button */}
                        <div className="flex-1">
                          {index === 0 ? (
                            /* Rooms & Guests for first segment */
                            <HotelRoomSelector
                              rooms={segment.rooms}
                              onRoomsChange={(rooms) => handleHotelSegmentRoomsChange(segment.id, rooms)}
                            />
                          ) : index === 1 ? (
                            /* Add Hotel Button for second segment */
                            <FlightActionButton
                              action="add"
                              onAction={addHotelSegment}
                              type="hotel"
                            />
                          ) : (
                            /* Remove Button for additional segments */
                            <FlightActionButton
                              action="remove"
                              onAction={removeHotelSegment}
                              segmentId={segment.id}
                              type="hotel"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              /* Single Hotel Layout */
              <div className="space-y-1">
                {/* Unified Layout - Stacked on Mobile, Row on Desktop */}
                <div className="flex flex-col lg:flex-row gap-2">
                  {/* Destination */}
                  <div className="w-full lg:flex-1">
                    <HotelDestinationSelector
                      destination={hotelDestination}
                      onDestinationChange={handleHotelDestinationChange}
                    />
                  </div>

                  {/* Check In Date */}
                  <div className="w-full lg:flex-1">
                    <CheckInDateSelector
                      checkInDate={checkInDate}
                      onDateChange={handleHotelDateChange}
                    />
                  </div>

                  {/* Check Out Date */}
                  <div className="w-full lg:flex-1">
                    <CheckOutDateSelector
                      checkInDate={checkInDate}
                      checkOutDate={checkOutDate}
                      onDateChange={handleHotelDateChange}
                    />
                  </div>

                  {/* Rooms & Guests */}
                  <div className="w-full lg:flex-1">
                    <HotelRoomSelector
                      rooms={hotelRooms}
                      onRoomsChange={handleHotelRoomsChange}
                    />
                  </div>
                </div>

                {/* Desktop/Laptop Layout - All in Same Line */}
                <div className="hidden">
                  <div className="flex flex-row gap-2 relative">
                    {/* Destination */}
                    <div className="flex-1">
                      <HotelDestinationSelector
                        destination={hotelDestination}
                        onDestinationChange={handleHotelDestinationChange}
                      />
                    </div>

                    {/* Check In Date */}
                    <div className="flex-1">
                      <CheckInDateSelector
                        checkInDate={checkInDate}
                        onDateChange={handleHotelDateChange}
                      />
                    </div>

                    {/* Check Out Date */}
                    <div className="flex-1">
                      <CheckOutDateSelector
                        checkInDate={checkInDate}
                        checkOutDate={checkOutDate}
                        onDateChange={handleHotelDateChange}
                      />
                    </div>

                    {/* Rooms & Guests */}
                    <div className="flex-1">
                      <HotelRoomSelector
                        rooms={hotelRooms}
                        onRoomsChange={handleHotelRoomsChange}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          /* Placeholder for other tabs */
          <div className="text-center py-4">
            <div className="text-[#0C2C7A] text-sm font-semibold mb-1">
              {bookingTabs.find(tab => tab.id === activeTab)?.label} Booking
            </div>
            <div className="text-gray-600 text-xs">
              Coming Soon! This booking type is under development.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Booking;