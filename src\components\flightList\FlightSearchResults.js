import React, { useState, useEffect, useMemo ,useRef } from "react";
import { ChevronDown } from 'lucide-react';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import FlightFilters from "./FlightFilters";
import FlightCard from "./FlightCard";
import { useSelector } from "react-redux";

const FlightSearchResults = () => {
  const { flights, loading, error } = useSelector((state) => state.flight);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortOpen, setSortOpen] = useState(false);
  const [selectedSort, setSelectedSort] = useState('Sort by Default');

  const [flightsPerPage] = useState(6);
  const languageRef = useRef(null);
  
  const [filters, setFilters] = useState({
    flightClass: [],
    priceRange: { min: 0, max: 10000 },
    timeSlots: [],
    stops: [],
    airlines: [],
    weights: [],
    refundable: [],
  });

  // Transform API response to match component expectations
  const transformFlightData = (apiFlights) => {
    if (!apiFlights || !Array.isArray(apiFlights)) return [];

    return apiFlights.map((flight, index) => {
      const segments = flight.segments || [];
      const isRoundTrip = flight.flightType === 'roundTrip' && segments.length >= 2;

      // For round trip, use first segment as outbound, second as return
      const outboundSegment = segments[0];
      const returnSegment = isRoundTrip ? segments[1] : null;

      // Parse baggage allowance
      const parseBaggage = (baggageString) => {
        const cabinMatch = baggageString?.match(/Cabin:\s*(\d+)kg/);
        const checkInMatch = baggageString?.match(/Check-in:\s*(\d+)kg/);
        return {
          cabin: cabinMatch ? `${cabinMatch[1]} kg` : "7 kg",
          checkIn: checkInMatch ? `${checkInMatch[1]} kg` : "20 kg"
        };
      };

      // Format time from ISO string
      const formatTime = (isoString) => {
        if (!isoString) return "00:00";
        const date = new Date(isoString);
        return date.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit', 
          hour12: false 
        });
      };

      // Format date from ISO string
      const formatDate = (isoString) => {
        if (!isoString) return "";
        const date = new Date(isoString);
        return date.toISOString().split('T')[0];
      };

      // Calculate stops
      const calculateStops = (segments) => {
        if (segments.length <= 1) return "Non Stop";
        return segments.length === 2 ? "One Stop" : `${segments.length - 1} Stops`;
      };

      // Map service class to display format
      const mapServiceClass = (serviceClass) => {
        switch (serviceClass?.toUpperCase()) {
          case 'BUSINESS': return 'Business';
          case 'FIRST': return 'First Class';
          case 'ECONOMY': return 'Economy';
          case 'PREMIUM_ECONOMY': return 'Premium Economy';
          default: return serviceClass || 'Economy';
        }
      };

      const baseFlight = {
        id: flight.id || `flight-${index}`,
        flightName: outboundSegment?.airlineDisplayName || "Unknown Airline",
        departureCity: outboundSegment?.departure?.city || "Unknown",
        departureCode: outboundSegment?.departure?.cityCode || "UNK",
        arrivalCity: outboundSegment?.arrival?.city || "Unknown",
        arrivalCode: outboundSegment?.arrival?.cityCode || "UNK",
        departureDate: formatDate(outboundSegment?.departure?.scheduledDeparture),
        arrivalDate: formatDate(outboundSegment?.arrival?.scheduledArrival),
        departureTime: formatTime(outboundSegment?.departure?.scheduledDeparture),
        arrivalTime: formatTime(outboundSegment?.arrival?.scheduledArrival),
        duration: outboundSegment?.duration || "0h 0m",
        baggage: parseBaggage(flight.baggageAllowance),
        flightClass: mapServiceClass(flight.serviceClass),
        stops: calculateStops(segments),
        price: flight.totalPrice || flight.price || 0,
        discount: Math.round((flight.discountRate || 0) * 100),
        image: outboundSegment?.airlineLogo || outboundSegment?.images?.[0] || "",
        refund: flight.refundable || "Partially Refundable",
        rating: outboundSegment?.airlineRating || 0,
        reviews: outboundSegment?.reviews || 0,
        isRoundTrip: isRoundTrip,
        isMultiCity: flight.flightType === 'multiCity' || segments.length > 2,
        segments: segments, // Keep original segments for reference
      };

      // Add return flight data for round trip
      if (isRoundTrip && returnSegment) {
        baseFlight.returnFlight = {
          departureTime: formatTime(returnSegment.departure?.scheduledDeparture),
          arrivalTime: formatTime(returnSegment.arrival?.scheduledArrival),
          departureDate: formatDate(returnSegment.departure?.scheduledDeparture),
          arrivalDate: formatDate(returnSegment.arrival?.scheduledArrival),
          duration: returnSegment.duration || "0h 0m",
          stops: calculateStops([returnSegment]),
          image: returnSegment.airlineLogo || returnSegment.images?.[0] || "",
        };
      }

      // Add multi-city flight segments
      if (baseFlight.isMultiCity && segments.length > 2) {
        baseFlight.multiCityFlights = segments.slice(1).map((segment, segIndex) => ({
          id: `segment-${segIndex + 1}`,
          flightName: segment?.airlineDisplayName || "Unknown Airline",
          departureCity: segment?.departure?.city || "Unknown",
          departureCode: segment?.departure?.cityCode || "UNK",
          arrivalCity: segment?.arrival?.city || "Unknown",
          arrivalCode: segment?.arrival?.cityCode || "UNK",
          departureDate: formatDate(segment?.departure?.scheduledDeparture),
          arrivalDate: formatDate(segment?.arrival?.scheduledArrival),
          departureTime: formatTime(segment?.departure?.scheduledDeparture),
          arrivalTime: formatTime(segment?.arrival?.scheduledArrival),
          duration: segment?.duration || "0h 0m",
          stops: calculateStops([segment]),
          image: segment?.airlineLogo || segment?.images?.[0] || "",
        }));
      }

      return baseFlight;
    });
  };

  const sortOptions = [
    { value: "Sort by Default", label: "Sort by Default" },
    { value: "Sort by Popular", label: "Sort by Popular" },
    { value: "Sort by Low price", label: "Sort by Low price" },
    { value: "Sort by High price", label: "Sort by High price" },
  ];

  
useEffect(() => {
    const handleClickOutside = (event) => {
      if (languageRef.current && !languageRef.current.contains(event.target)) {
        setSortOpen(false);
      }

    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  const handleFilterChange = (filterType, value, checked) => {
    setFilters((prev) => {
      const newFilters = { ...prev };

      switch (filterType) {
        case "priceRange":
          newFilters.priceRange = value;
          break;

        case "flightClass":
        case "timeSlots":
        case "stops":
        case "airlines":
        case "weights":
        case "refundable":
          if (checked) {
            newFilters[filterType] = [...prev[filterType], value];
          } else {
            newFilters[filterType] = prev[filterType].filter(
              (item) => item !== value
            );
          }
          break;

        default:
          break;
      }

      return newFilters;
    });

    // Reset to first page when filters change
    setCurrentPage(1);
  };

  const getTimeSlot = (time) => {
    if (!time) return null;
    const hour = parseInt(time.split(":")[0]);

    if (hour >= 0 && hour < 6) return "early-morning";
    if (hour >= 6 && hour < 12) return "morning";
    if (hour >= 12 && hour < 18) return "afternoon";
    if (hour >= 18 && hour < 24) return "evening";

    return null;
  };

  const mapRefundStatus = (refund) => {
    if (!refund) return "Non Refundable";

    const refundLower = refund.toLowerCase();
    if (refundLower.includes("refundable")) {
      if (refundLower.includes("partially")) return "As Per Rules";
      if (refundLower.includes("not")) return "Non Refundable";
      return "Refundable";
    }
    if (refundLower.includes("not")) return "Non Refundable";

    return "As Per Rules";
  };

  const transformedFlights = useMemo(() => {
    return transformFlightData(flights);
  }, [flights]);

  const filteredFlights = useMemo(() => {
    return transformedFlights.filter((flight) => {
      // Filter by flight class
      if (filters.flightClass.length > 0) {
        if (!filters.flightClass.includes(flight.flightClass)) {
          return false;
        }
      }

      // Filter by price range
      const finalPrice =
        flight.price - (flight.price * (flight.discount || 0)) / 100;
      if (
        finalPrice < filters.priceRange.min ||
        finalPrice > filters.priceRange.max
      ) {
        return false;
      }

      // Filter by time slots
      if (filters.timeSlots.length > 0) {
        const departureTimeSlot = getTimeSlot(flight.departureTime);
        if (!filters.timeSlots.includes(departureTimeSlot)) {
          return false;
        }
      }

      // Filter by stops
      if (filters.stops.length > 0) {
        if (!filters.stops.includes(flight.stops)) {
          return false;
        }
      }

      // Filter by airlines (using flight name)
      if (filters.airlines.length > 0) {
        // For now, using flight name as airline filter
        // You can enhance this to match actual airline names
        const hasMatchingAirline = filters.airlines.some((airline) =>
          flight.flightName
            .toLowerCase()
            .includes(airline.toLowerCase().split(" ")[0])
        );
        if (!hasMatchingAirline) {
          return false;
        }
      }

      // Filter by refundable status
      if (filters.refundable.length > 0) {
        const refundStatus = mapRefundStatus(flight.refund);
        if (!filters.refundable.includes(refundStatus)) {
          return false;
        }
      }

      return true;
    });
  }, [filters, transformedFlights]);

  // Pagination logic
  const totalPages = Math.ceil(filteredFlights.length / flightsPerPage);
  const indexOfLastFlight = currentPage * flightsPerPage;
  const indexOfFirstFlight = indexOfLastFlight - flightsPerPage;
  const currentFlights = filteredFlights.slice(
    indexOfFirstFlight,
    indexOfLastFlight
  );

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Scroll to top of flight results
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-4 md:p-6">
      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#24BDC7] mx-auto mb-4"></div>
            <p className="text-gray-600">Loading flights...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="text-center">
            <p className="text-red-600 font-semibold">Error loading flights</p>
            <p className="text-red-500 text-sm mt-1">{error}</p>
          </div>
        </div>
      )}

      {/* No Data State */}
      {!loading && !error && (!flights || flights.length === 0) && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg mb-2">No flights found</div>
          <p className="text-gray-400">Please try a different search criteria</p>
        </div>
      )}

      {/* Main Content - Only show if we have data and not loading */}
      {!loading && !error && flights && flights.length > 0 && (
        <>
          {/* Mobile Layout - Filters First */}
          <div className="block lg:hidden">
        {/* Mobile Filters Section - Always visible on mobile */}
        <div className="mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <FlightFilters
              onFilterChange={handleFilterChange}
              filters={filters}
            />
          </div>
        </div>

        {/* Mobile Flight Results */}
        <div className="w-full">
          {/* Result count */}
          <div className="flex justify-between items-center mb-4 bg-white rounded-2xl pl-5 p-3">
            <p className="text-sm font-semibold text-[#0C2C82]">
              {filteredFlights.length} Results Found
            </p>
            {/* Sort Dropdown */}
            <div className="relative" ref={languageRef}>
              <button
                onClick={() => setSortOpen(!sortOpen)}
                className="flex items-center justify-center gap-2 px-2 py-2 font-semibold text-[#0C2C82] border border-[#EBEBEB] hover:border-[#24BDC7] rounded-xl transition-colors duration-200 text-xs"
              >
                {selectedSort}
                <ChevronDown size={12} className={`transition-transform duration-200 ${sortOpen ? 'rotate-180' : ''}`} />
              </button>
              {sortOpen && (
                <div className="absolute top-full mt-1 bg-white text-[#0C2C82] rounded-md shadow-lg border border-gray-200 py-1 z-50 right-0 min-w-max">
                  {sortOptions.map((sort) => (
                    <button
                      key={sort.value}
                      onClick={() => {
                        setSelectedSort(sort.value);
                        setSortOpen(false);
                      }}
                      className="w-full text-left px-3 py-2 font-semibold hover:text-[#24BDC7] hover:bg-gray-100 transition-colors duration-150 text-xs whitespace-nowrap"
                    >
                      {sort.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="space-y-3">
            {currentFlights.map((flight, index) => (
              <FlightCard key={flight.id || index} flight={flight} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6 flex items-center justify-center space-x-2">
              <button
                onClick={handlePrevPage}
                disabled={currentPage === 1}
                className={`flex items-center justify-center w-8 h-8 rounded-lg border transition-colors bg-[#0C2C7A] text-white hover:bg-[#24BDC7] ${
                  currentPage === 1 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                <ChevronLeftIcon className="h-3 w-3" />
              </button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (pageNumber) => (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`w-8 h-8 rounded-lg border transition-colors text-xs ${
                        currentPage === pageNumber
                          ? "bg-[#24BDC7] text-white"
                          : "bg-[#0C2C7A] text-white hover:bg-[#24BDC7]"
                      }`}
                    >
                      {pageNumber}
                    </button>
                  )
                )}
              </div>

              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
                className={`flex items-center justify-center w-8 h-8 rounded-lg border transition-colors bg-[#0C2C7A] text-white hover:bg-[#24BDC7] ${
                  currentPage === totalPages
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                <ChevronRightIcon className="h-3 w-3" />
              </button>
            </div>
          )}

          {filteredFlights.length === 0 && (
            <div className="text-center py-8">
              <div className="text-gray-500 text-base">
                No flights found for your search criteria
              </div>
              <p className="text-gray-400 mt-2 text-sm">
                Try adjusting your filters to see more results
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Desktop Layout - Side by Side */}
      <div className="hidden lg:flex lg:flex-row gap-6">
        {/* Desktop Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <FlightFilters
            onFilterChange={handleFilterChange}
            filters={filters}
          />
        </div>

        {/* Desktop Flight Results */}
        <div className="flex-1">
          {/* Result count */}
          <div className="flex justify-between items-center mb-6 bg-white rounded-2xl pl-5 p-3">
            <p className="text-base font-semibold text-[#0C2C82]">
              {filteredFlights.length} Results Found
            </p>
            {/* Sort Dropdown */}
            <div className="relative" ref={languageRef}>
              <button
                onClick={() => setSortOpen(!sortOpen)}
                className="flex items-center justify-center gap-8 px-4 py-2 font-semibold text-[#0C2C82] border border-[#EBEBEB] hover:border-[#24BDC7] rounded-xl transition-colors duration-200"
              >
                {selectedSort}
                <ChevronDown size={14} className={`transition-transform duration-200 ${sortOpen ? 'rotate-180' : ''}`} />
              </button>
              {sortOpen && (
                <div className="absolute top-full mt-1 bg-white text-[#0C2C82] rounded-md shadow-lg border border-gray-200 py-1 z-50 right-0">
                  {sortOptions.map((sort) => (
                    <button
                      key={sort.value}
                      onClick={() => {
                        setSelectedSort(sort.value);
                        setSortOpen(false);
                      }}
                      className="w-full text-left px-3 py-2 font-semibold hover:text-[#24BDC7] hover:bg-gray-100 transition-colors duration-150 text-sm"
                    >
                      {sort.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="space-y-4">
            {currentFlights.map((flight, index) => (
              <FlightCard key={flight.id || index} flight={flight} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6 flex items-center justify-center space-x-4">
              <button
                onClick={handlePrevPage}
                disabled={currentPage === 1}
                className={`flex items-center justify-center w-10 h-10 rounded-lg border transition-colors bg-[#0C2C7A] text-white hover:bg-[#24BDC7] ${
                  currentPage === 1 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>

              <div className="flex items-center space-x-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (pageNumber) => (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`w-10 h-10 rounded-lg border transition-colors ${
                        currentPage === pageNumber
                          ? "bg-[#24BDC7] text-white"
                          : "bg-[#0C2C7A] text-white hover:bg-[#24BDC7]"
                      }`}
                    >
                      {pageNumber}
                    </button>
                  )
                )}
              </div>

              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
                className={`flex items-center justify-center w-10 h-10 rounded-lg border transition-colors bg-[#0C2C7A] text-white hover:bg-[#24BDC7] ${
                  currentPage === totalPages
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          )}

          {filteredFlights.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">
                No flights found for your search criteria
              </div>
              <p className="text-gray-400 mt-2 text-base">
                Try adjusting your filters to see more results
              </p>
            </div>
          )}
        </div>
      </div>
        </>
      )}
    </div>
  );
};

export default FlightSearchResults;
