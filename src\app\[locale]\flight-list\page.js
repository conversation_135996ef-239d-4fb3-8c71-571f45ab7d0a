"use client"
import FlightSearchResults from "@/components/flightList/FlightSearchResults";
import Header from "@/components/home/<USER>";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { searchFlights } from "@/redux/slices/flightSlice";

export default function FlightListPage() {
  const breadcrumbItems = [
    { label: 'Flight List', href: '/flight-list' }
  ];

  const bookingData = useSelector((state) => state.booking);
  console.log("Booking data from Redux store:", bookingData);

  const dispatch = useDispatch();

  
  // useEffect(() => {
  //   if (bookingData && bookingData.fromLocation && bookingData.toLocation) {
  //     const payload = {
  //       flightType: bookingData.tripType === 'roundTrip' ? 'roundTrip' : bookingData.tripType === 'multiCity' ? 'multiCity' : 'oneWay',
  //       legs: [], // Start with empty array
  //     };

  //     console.log("Flight search payload:", payload);
  //     dispatch(searchFlights(payload));
  //   }
  // }, [dispatch, bookingData]);


  useEffect(() => {
    if (bookingData && bookingData.fromLocation && bookingData.toLocation) {
      const payload = {
        flightType: bookingData.tripType === 'roundTrip' ? 'roundTrip' : bookingData.tripType === 'multiCity' ? 'multiCity' : 'oneWay',
        legs: [], // Start with empty array
      };

      if (bookingData.tripType === 'multiCity' && bookingData.flightSegments) {
        // Handle multi-city
        payload.legs = bookingData.flightSegments.map(segment => (
          {
          from: segment.fromLocation.cityCode,
          to: segment.toLocation.cityCode,
          date: new Date(new Date(segment.departureDate).getTime() - new Date(segment.departureDate).getTimezoneOffset() * 60000).toISOString().split('T')[0], // Format as YYYY-MM-DD without timezone shift
        }));
      } else {
        // Handle one-way or round-trip
        payload.legs.push({
          from: bookingData.fromLocation.cityCode,
          to: bookingData.toLocation.cityCode,
          date: new Date(new Date(bookingData.departureDate).getTime() - new Date(bookingData.departureDate).getTimezoneOffset() * 60000).toISOString().split('T')[0],
        });

        if (bookingData.tripType === 'roundTrip' && bookingData.returnDate) {
          payload.legs.push({
            from: bookingData.toLocation.cityCode,
            to: bookingData.fromLocation.cityCode,
            date: new Date(new Date(bookingData.returnDate).getTime() - new Date(bookingData.returnDate).getTimezoneOffset() * 60000).toISOString().split('T')[0],
          });
        }
      }

      console.log("Flight search payload:", payload);
      dispatch(searchFlights(payload));
    }
  }, [dispatch, bookingData]);

  const { flights, loading, error } = useSelector((state) => state.flight);
  console.log("Flights data from Redux store:", flights, loading, error);
  return (
    <div className="bg-[#F6F6F6]">
      <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Flight List"
        heroSubtitle="Find and book the perfect flight for your journey"
        showBooking={true}
        className="-mt-30"
        breadcrumbItems={breadcrumbItems}
      /> 
      <div className="mt-20 mb-20">
        <FlightSearchResults />
      </div>
    </div>
  );
}
