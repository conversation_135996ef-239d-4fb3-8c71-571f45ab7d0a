// src/redux/slices/flightDetailSlice.js
import { fetchFlightDetails } from "@/api/flightDetailApi";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

// Async thunk for fetching flight details
export const fetchFlightDetail = createAsyncThunk(
  "flightDetail/fetchFlightDetail",
  async (_, { rejectWithValue }) => {
    const fareId = "fare-0099";
    try {
      const res = await fetchFlightDetails(fareId);
      console.log("Fetched flight detail:", res.data); // Debugging response
      return res.data;
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message || "Failed to fetch flight details"
      );
    }
  }
);

const flightDetailSlice = createSlice({
  name: "flightDetail",
  initialState: {
    detail: null, // flight detail object
    loading: false,
    error: null,
  },
  reducers: {
    clearFlightDetail: (state) => {
      state.detail = null;
      state.error = null;
      state.loading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFlightDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFlightDetail.fulfilled, (state, action) => {
        state.loading = false;
        console.log("Action payload (flightDetail):", action.payload);
        state.detail = action.payload;
      })
      .addCase(fetchFlightDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearFlightDetail } = flightDetailSlice.actions;
export default flightDetailSlice.reducer;
